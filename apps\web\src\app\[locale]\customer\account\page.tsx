'use client'
import { useCallback, useEffect, useState } from 'react'
import {
  Customer,
  resolveCatchMessage,
  TRACK_EVENT,
  useLazyGetAccountCenterInfoQuery,
  useToastContext,
  useVolcAnalytics,
  GqlError,
  PRECISE_RATE_LIMIT,
} from '@ninebot/core'
import { useRouter } from '@/i18n/navigation'

import { OrderList } from '@/businessComponents'
import { Skeleton, UserProfile } from '@/components'

export default function AccountPage() {
  const toast = useToastContext()
  const { reportEvent } = useVolcAnalytics()
  const router = useRouter()

  const [loading, setLoading] = useState({
    customerLoading: true,
    orderStatusLoading: true,
    ordersLoading: true,
  })
  const [getAccountCenterInfo] = useLazyGetAccountCenterInfoQuery()
  const [customer, setCustomer] = useState<Customer>({
    info: {
      email: '',
      name: '',
      phone: '',
      nCoin: 0,
      avatar: '',
    },
    orders: {
      pending: '0',
      processing: '0',
      shipped: '0',
      complete: '0',
    },
    services: {
      coupons: [],
    },
  })

  const getAccountInfo = useCallback(() => {
    getAccountCenterInfo({})
      .unwrap()
      .then((res) => {
        const accountInfo = res
        if (accountInfo?.customer) {
          const currentCustomer = accountInfo.customer
          const currentCouponsData = accountInfo.customer_coupons.items
          setCustomer((prevState) => ({
            ...prevState,
            info: {
              email: currentCustomer?.customer_email,
              name: currentCustomer?.customer_info?.customer_nickname,
              phone: currentCustomer?.customer_phone,
              nCoin: currentCustomer?.customer_ncoin,
              avatar: currentCustomer?.customer_info?.customer_logo,
            },
            orders: accountInfo.order,
            services: {
              coupons: currentCouponsData,
            },
          }))
          setLoading((pre) => ({
            ...pre,
            customerLoading: false,
          }))
        }
      })
      .catch((error) => {
        const err = error as GqlError
        const errorMessage = resolveCatchMessage(error) as string

        // getCustomerInfo接口427/428错误：toast（3秒） + message信息 + toast消失后重定向到首页
        if (err?.type === PRECISE_RATE_LIMIT && [427, 428].includes(err.status)) {
          // TODO: 这里可以添加message弹窗显示逻辑
          console.error('getCustomerInfo rate limited:', errorMessage)

          toast.show({
            icon: 'fail',
            content: errorMessage,
            duration: 3000,
          })

          // toast消失后重定向到首页
          setTimeout(() => {
            router.push('/')
          }, 3000)
        } else {
          // 其他错误正常处理
          toast.show({
            icon: 'fail',
            content: errorMessage,
          })
        }
      })
  }, [getAccountCenterInfo, toast, router])

  useEffect(() => {
    getAccountInfo()
  }, [getAccountInfo])

  /**
   * 埋点：点击个人中心
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_profile_page_exposure)
  }, [reportEvent])

  return (
    <div className="flex-1">
      {loading.customerLoading ? (
        <div className="mb-base-16 flex h-[327px] flex-col rounded-[20px] bg-white p-base-32">
          <div className="mb-[48px] flex h-[117px] w-full flex-row items-center justify-between">
            <div className="flex w-[64%] gap-base-24">
              <Skeleton shape="circle" style={{ width: 108, height: 108 }} />
              <Skeleton style={{ width: '40%', height: '108px' }} />
            </div>
            <Skeleton style={{ width: '33%' }} />
          </div>
          <div className="flex h-[170px] flex-row items-center justify-between">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} style={{ width: '20%' }} />
            ))}
          </div>
        </div>
      ) : (
        <UserProfile customer={customer} />
      )}

      <OrderList isPage={false} externalStatus="" />
    </div>
  )
}
