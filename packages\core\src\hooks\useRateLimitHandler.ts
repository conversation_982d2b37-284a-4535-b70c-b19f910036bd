'use client'

import { useCallback, useEffect, useState } from 'react'

import { useToastContext } from '../providers'

/**
 * 限流处理模式
 */
export type TRateLimitMode = 'button' | 'toast' | 'silent'

/**
 * 限流处理配置
 */
export type TRateLimitConfig = {
  mode: TRateLimitMode
  originalText?: string // 按钮原始文案
  showMessage?: boolean // 是否显示message信息
}

/**
 * 限流处理结果
 */
export type TRateLimitResult = {
  isRateLimited: boolean
  countdown: number
  buttonText: string
  isDisabled: boolean
}

/**
 * 限流处理Hook
 */
export const useRateLimitHandler = (
  config: TRateLimitConfig,
): TRateLimitResult & {
  handleError: (retryMs: number, message?: string) => void
  reset: () => void
} => {
  const { mode, originalText = '', showMessage = true } = config
  const toast = useToastContext()

  const [isRateLimited, setIsRateLimited] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null)

  /**
   * 清理定时器
   */
  const clearTimer = useCallback(() => {
    if (intervalId) {
      clearInterval(intervalId)
      setIntervalId(null)
    }
  }, [intervalId])

  /**
   * 开始倒计时
   */
  const startCountdown = useCallback((retryMs: number) => {
    setIsRateLimited(true)
    setCountdown(Math.ceil(retryMs / 1000))

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setIsRateLimited(false)
          clearInterval(timer)
          setIntervalId(null)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    setIntervalId(timer)
  }, [])

  /**
   * 处理错误
   */
  const handleError = useCallback(
    (retryMs: number, message?: string) => {
      const errorMessage = message || '当前访问人数过多，请稍后再试！'

      if (mode === 'silent') {
        // 静默模式：不显示任何提示
        return
      }

      if (mode === 'button') {
        // Button模式：开始倒计时
        startCountdown(retryMs)

        // 显示toast和message信息
        if (showMessage) {
          toast.show({
            icon: 'fail',
            content: errorMessage,
            duration: 3000,
          })
        }
      } else if (mode === 'toast') {
        // Toast模式：只显示toast
        toast.show({
          icon: 'fail',
          content: errorMessage,
          duration: 3000,
        })
      }
    },
    [startCountdown, mode, showMessage, toast],
  )

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    clearTimer()
    setIsRateLimited(false)
    setCountdown(0)
  }, [clearTimer])

  /**
   * 生成按钮文案
   */
  const buttonText =
    isRateLimited && mode === 'button' ? `${originalText}（${countdown}s）` : originalText

  /**
   * 组件卸载时清理定时器
   */
  useEffect(() => {
    return () => {
      clearTimer()
    }
  }, [clearTimer])

  return {
    isRateLimited,
    countdown,
    buttonText,
    isDisabled: isRateLimited && mode === 'button',
    handleError,
    reset,
  }
}

export default useRateLimitHandler
