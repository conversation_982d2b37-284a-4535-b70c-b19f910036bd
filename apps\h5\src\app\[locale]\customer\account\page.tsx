'use client'
import { useCallback, useEffect, useState } from 'react'
import {
  resolveCatchMessage,
  TRACK_EVENT,
  useLazyGetAccountCenterInfoQuery,
  useToastContext,
  useVolcAnalytics,
  GqlError,
  PRECISE_RATE_LIMIT,
  useNavigate,
} from '@ninebot/core'
import { Customer } from '@ninebot/core'
import { ROUTE } from '@ninebot/core/src/constants'

import { AccountCenter, ServiceButtons } from '@/components'
import { AccountSkeleton } from '@/components/business/user/components'

export default function AccountPage() {
  const [getAccountCenterInfo, { isLoading }] = useLazyGetAccountCenterInfoQuery()
  const [customer, setCustomer] = useState<Customer>({
    info: {
      email: '',
      name: '',
      phone: '',
      nCoin: 0,
      avatar: '',
    },
    orders: {
      pending: '0',
      processing: '0',
      shipped: '0',
      complete: '0',
    },
    services: {
      coupons: [],
    },
  })
  const toast = useToastContext()
  const { reportEvent } = useVolcAnalytics()
  const { openPage } = useNavigate()

  const getAccountInfo = useCallback(() => {
    getAccountCenterInfo({})
      .unwrap()
      .then((res) => {
        const accountInfo = res
        if (accountInfo?.customer) {
          const currentCustomer = accountInfo.customer
          const currentCouponsData = accountInfo.customer_coupons.items
          setCustomer((prevState) => ({
            ...prevState,
            info: {
              email: currentCustomer?.customer_email,
              name: currentCustomer?.customer_info?.customer_nickname,
              phone: currentCustomer?.customer_phone,
              nCoin: currentCustomer?.customer_ncoin,
              avatar: currentCustomer?.customer_info?.customer_logo,
            },
            orders: accountInfo.order,
            services: {
              coupons: currentCouponsData,
            },
          }))
        }
      })
      .catch((error) => {
        const err = error as GqlError
        const errorMessage = resolveCatchMessage(error) as string

        // getCustomerInfo接口427/428错误：toast（3秒） + message信息 + toast消失后重定向到首页
        if (err?.type === PRECISE_RATE_LIMIT && [427, 428].includes(err.status)) {
          // TODO: 这里可以添加message弹窗显示逻辑
          console.error('getCustomerInfo rate limited:', errorMessage)

          toast.show({
            icon: 'fail',
            content: errorMessage,
            duration: 3000,
          })

          // toast消失后重定向到首页
          setTimeout(() => {
            openPage({
              route: ROUTE.home,
              replace: true,
            })
          }, 3000)
        } else {
          // 其他错误正常处理
          toast.show({
            icon: 'fail',
            content: errorMessage,
          })
        }
      })
  }, [getAccountCenterInfo, toast, openPage])

  useEffect(() => {
    getAccountInfo()
  }, [getAccountInfo])

  /**
   * 埋点：点击个人中心
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_profile_page_exposure)
  }, [reportEvent])

  return (
    <div className="py-[12px]">
      {isLoading || !customer.info ? (
        <AccountSkeleton />
      ) : (
        <>
          <AccountCenter customer={customer} />
          <ServiceButtons />
        </>
      )}
    </div>
  )
}
